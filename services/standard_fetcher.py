"""
编码规范获取服务

实现从外部API获取编码规范并缓存到本地的功能
"""

import hashlib
import json
import logging
from datetime import datetime
from typing import Any, Dict, Optional

import httpx

from ..config.settings import config
from ..models.standard import StandardDocument, StandardMetadata, StandardSelector
from ..storage.cache_manager import cache_manager
from ..storage.file_manager import file_manager

logger = logging.getLogger("CodeStandardMCP.StandardFetcher")


class StandardFetcher:
    """编码规范获取服务类"""

    def __init__(self):
        self.http_timeout = config.http_timeout
        self.user_agent = config.user_agent
        self.max_retries = config.max_retries

    async def fetch_standard(
        self,
        selector: StandardSelector,
        force_refresh: bool = False,
    ) -> Dict[str, Any]:
        """
        获取编码规范

        Args:
            selector: 规范选择器
            force_refresh: 是否强制刷新

        Returns:
            包含规范内容和元数据的字典
        """
        try:
            logger.info(f"Fetching standard: {selector.to_cache_key()}")

            # 检查缓存
            if not force_refresh:
                cached_document = await self._get_cached_standard(selector)
                if cached_document:
                    logger.info("Using cached standard content")
                    return {
                        "success": True,
                        "source": "cache",
                        "content": cached_document.content,
                        "metadata": cached_document.metadata.model_dump(),
                        "cached": True,
                    }

            # 从远程获取
            content = await self._fetch_from_remote(selector)
            if not content:
                return {
                    "success": False,
                    "error": "Failed to fetch from remote API",
                    "error_type": "fetch_failed",
                }

            # 创建标准文档
            document = await self._create_standard_document(selector, content)

            # 保存到缓存
            await self._save_to_cache(document)

            return {
                "success": True,
                "source": "remote",
                "content": document.content,
                "metadata": document.metadata.model_dump(),
                "cached": False,
            }

        except Exception as e:
            logger.error(f"Error fetching standard: {e}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "unexpected_error",
            }

    async def _get_cached_standard(self, selector: StandardSelector) -> Optional[StandardDocument]:
        """获取缓存的标准文档"""
        try:
            cache_key = selector.to_cache_key()
            cache_file_path = config.get_cache_file_path(cache_key)
            
            if not cache_file_path.exists():
                return None
                
            data = await file_manager.read_json(cache_file_path)
            if not data:
                return None
                
            return StandardDocument.model_validate(data)
        except Exception as e:
            logger.error(f"Error reading cached standard: {e}")
            return None

    async def _fetch_from_remote(self, selector: StandardSelector) -> Optional[str]:
        """从远程API获取内容"""
        try:
            # 构建API URL
            api_url = self._build_api_url(selector)
            if not api_url:
                logger.error(f"No API endpoint configured for {selector.language}")
                return None

            # 发送HTTP请求
            async with httpx.AsyncClient(timeout=self.http_timeout) as client:
                headers = {"User-Agent": self.user_agent}
                response = await client.get(api_url, headers=headers)
                response.raise_for_status()
                return response.text

        except Exception as e:
            logger.error(f"Error fetching from remote: {e}")
            return None

    def _build_api_url(self, selector: StandardSelector) -> Optional[str]:
        """构建API URL"""
        # 这里应该根据实际的API端点配置来构建URL
        # 暂时返回一个示例URL
        base_url = "https://api.example.com/standards"
        params = [f"language={selector.language}"]
        
        if selector.framework:
            params.append(f"framework={selector.framework}")
        if selector.version:
            params.append(f"version={selector.version}")
            
        return f"{base_url}?{'&'.join(params)}"

    async def _create_standard_document(self, selector: StandardSelector, content: str) -> StandardDocument:
        """创建标准文档"""
        # 计算内容哈希
        content_hash = hashlib.md5(content.encode()).hexdigest()
        
        # 创建元数据
        metadata = StandardMetadata(
            selector=selector,
            fetch_date=datetime.now(),
            source_url=self._build_api_url(selector),
            content_hash=content_hash,
        )
        
        return StandardDocument(metadata=metadata, content=content)

    async def _save_to_cache(self, document: StandardDocument) -> bool:
        """保存到缓存"""
        try:
            cache_key = document.metadata.selector.to_cache_key()
            cache_file_path = config.get_cache_file_path(cache_key)
            
            # 确保缓存目录存在
            cache_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存文档
            data = document.model_dump()
            success = await file_manager.write_json(cache_file_path, data)
            
            if success:
                logger.info(f"Standard cached successfully: {cache_key}")
            
            return success
        except Exception as e:
            logger.error(f"Error saving to cache: {e}")
            return False


# 创建全局实例
standard_fetcher = StandardFetcher()
